<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8">

	<title>xSlides</title>
	<link rel="icon" href="/favicon.png" />
	<meta name="description" content="A framework for easily creating beautiful presentations using Markdown">

	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

	<meta name="viewport" content="width=device-width, initial-scale=1.0">

	<link rel="stylesheet" href="dist/reset.css">
	<link rel="stylesheet" href="dist/reveal.css">
	<link rel="stylesheet" href="dist/theme/white.css" id="theme">
	<link rel="stylesheet" href="/css/index.css">

	<link rel="stylesheet" href="plugins-advanced/poll/style.css">
	<link rel="stylesheet" href="plugin/article/plugin.css">
	<!-- Theme used for syntax highlighting of code -->
	<!-- <link rel="stylesheet" href="plugin/highlight/monokai.css"> -->
	<script src="js/util.js"></script>

	<script>
		var mode = getUrlParameter("mode");
	</script>
	<style>
		:root {
			--r-heading-text-transform: none;
			--r-main-font-size: 24px;        /* 主要文本字体大小 */
			--r-heading1-size: 2.0em;        /* h1 标题大小 */
			--r-heading2-size: 1.5em;        /* h2 标题大小 */
			--r-heading3-size: 1.2em;        /* h3 标题大小 */
			--r-heading4-size: 0.9em;        /* h4 标题大小 */
			--r-block-margin: 12px;          /* 段落间距 */
		}

		.reveal .slides section ul,
		.reveal .slides section ol { 
			margin-left: 0;
		}
	</style>
</head>

<body style="background-color:gray; display: flex; flex-direction: column;">

	<!-- Any section element inside of this container is displayed as a slide -->
	<div class="reveal" id="reveal">
		<div class="slides reveal-viewport" id='slides'></div>
	</div>


	<script src="dist/reveal.js"></script>
	<script src="plugin/zoom/zoom.js"></script>
	<script src="plugin/notes/notes.js"></script>
	<script src="plugin/math/math.js"></script>
	<script src="plugin/cardify/plugin.js"></script>
	<script src="plugin/search/search.js"></script>
	<script src="plugin/markdown/markdown.js"></script>
	<script src="plugin/fullscreen/plugin.js"></script>
	<script src="plugin/videorender/plugin.js"></script>
	<script src="plugin/article/plugin.js"></script>
	<!-- <script src="plugin/highlight/highlight.js"></script> -->

	<script src="lib/chart-3.2.0.min.js"></script>
	<script src="plugins-advanced/chart/plugin.js"></script>

	<script src="lib/js/head.min.js"></script>
	<script src="/javascripts/jquery.min.js"></script>
	<script src="js/vmview.js"></script>
	<script src="lib/html2canvas.min.js"></script>
</body>

</html>