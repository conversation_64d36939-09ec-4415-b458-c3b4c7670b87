/**
 * Reveal.js 图文智能混排插件
 * 自动检测并重新排列幻灯片中的图片和文本内容
 */

const SmartLayout = {
    id: 'smartlayout',
    
    init: function(reveal) {
        // 添加必要的CSS样式
        this.addStyles();
        
        // 监听幻灯片变化事件
        reveal.addEventListener('slidechanged', this.handleSlideChange.bind(this));
        reveal.addEventListener('ready', this.handleReady.bind(this, reveal));
        
        console.log('SmartLayout plugin initialized');
    },
    
    // 添加插件所需的CSS样式
    addStyles: function() {
        const style = document.createElement('style');
        style.textContent = `
            /* 主容器 */
            .smart-layout-main {
                display: flex;
                flex-direction: column;
                height: 100%;
                width: 100%;
            }
            
            /* 标题区域 */
            .smart-layout-title {
                flex-shrink: 0;
                margin-bottom: 1.5rem;
                text-align: center;
            }
            
            .smart-layout-title h1,
            .smart-layout-title h2,
            .smart-layout-title h3,
            .smart-layout-title h4,
            .smart-layout-title h5,
            .smart-layout-title h6 {
                margin-top: 0;
                margin-bottom: 0.5rem;
            }
            
            /* 智能布局容器 */
            .smart-layout-container {
                display: flex;
                align-items: flex-start;
                gap: 2rem;
                flex: 1;
                min-height: 0;
            }
            
            /* 文本区域 */
            .smart-layout-text {
                flex: 1;
                min-width: 40%;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
            
            /* 图片区域 */
            .smart-layout-images {
                flex: 1;
                min-width: 40%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 1rem;
            }
            
            /* 图片样式优化 */
            .smart-layout-images img {
                max-width: 100%;
                max-height: 70vh;
                object-fit: contain;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }
            
            /* 响应式调整 */
            @media (max-width: 768px) {
                .smart-layout-container {
                    flex-direction: column;
                    gap: 1rem;
                }
                
                .smart-layout-text,
                .smart-layout-images {
                    min-width: 100%;
                }
                
                .smart-layout-images img {
                    max-height: 40vh;
                }
            }
            
            /* 多图片布局 */
            .smart-layout-images.multiple-images {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }
            
            .smart-layout-images.multiple-images img {
                max-height: 30vh;
            }
            
            /* 保持原有样式的文本元素 */
            .smart-layout-text h1,
            .smart-layout-text h2,
            .smart-layout-text h3,
            .smart-layout-text h4,
            .smart-layout-text h5,
            .smart-layout-text h6 {
                margin-top: 0;
            }
            
            /* 列表优化 */
            .smart-layout-text ul,
            .smart-layout-text ol {
                padding-left: 1.5rem;
            }
        `;
        document.head.appendChild(style);
    },
    
    // 处理初始化完成事件
    handleReady: function(reveal) {
        this.processAllSlides();
    },
    
    // 处理幻灯片切换事件
    handleSlideChange: function(event) {
        const currentSlide = event.currentSlide;
        this.processSlide(currentSlide);
    },
    
    // 处理所有幻灯片
    processAllSlides: function() {
        const slides = document.querySelectorAll('.reveal .slides section');
        slides.forEach(slide => {
            this.processSlide(slide);
        });
    },
    
    // 处理单个幻灯片
    processSlide: function(slide) {
        // 跳过已处理的幻灯片
        if (slide.classList.contains('smart-layout-processed')) {
            return;
        }
        
        // 查找标题、图片和文本元素
        const titleElements = this.getTitleElements(slide);
        const images = slide.querySelectorAll('img');
        const textElements = this.getTextElements(slide);
        
        // 只有在同时存在图片和文本时才进行重排
        if (images.length > 0 && textElements.length > 0) {
            this.rearrangeContent(slide, images, textElements, titleElements);
        }
        
        // 标记为已处理
        slide.classList.add('smart-layout-processed');
    },
    
    // 获取标题元素
    getTitleElements: function(slide) {
        return Array.from(slide.children).filter(element => {
            const tagName = element.tagName.toLowerCase();
            return ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName);
        });
    },
    
    // 获取文本元素（排除图片和标题）
    getTextElements: function(slide) {
        const allElements = Array.from(slide.children);
        return allElements.filter(element => {
            // 排除script、style、img、标题等元素
            const tagName = element.tagName.toLowerCase();
            return !['img', 'script', 'style', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName) && 
                   element.textContent.trim() !== '';
        });
    },
    
    // 重新排列内容
    rearrangeContent: function(slide, images, textElements, titleElements) {
        // 创建主容器来包含标题和内容区域
        const mainContainer = document.createElement('div');
        mainContainer.className = 'smart-layout-main';
        
        // 创建标题区域
        const titleArea = document.createElement('div');
        titleArea.className = 'smart-layout-title';
        
        // 创建智能布局容器（用于图文内容）
        const contentContainer = document.createElement('div');
        contentContainer.className = 'smart-layout-container';
        
        // 创建文本区域
        const textArea = document.createElement('div');
        textArea.className = 'smart-layout-text';
        
        // 创建图片区域
        const imageArea = document.createElement('div');
        imageArea.className = 'smart-layout-images';
        
        // 如果有多个图片，使用网格布局
        if (images.length > 1) {
            imageArea.classList.add('multiple-images');
        }
        
        // 移动标题元素到标题区域
        titleElements.forEach(element => {
            titleArea.appendChild(element.cloneNode(true));
        });
        
        // 移动文本元素到文本区域
        textElements.forEach(element => {
            textArea.appendChild(element.cloneNode(true));
        });
        
        // 移动图片到图片区域
        images.forEach(img => {
            if (!img.hasAttribute('data-preview-image')) {
                img.setAttribute('data-preview-image', ''); // 设置你想要的默认值
            }
            imageArea.appendChild(img.cloneNode(true));
        });
        
        // 根据原始位置关系决定布局方向
        const textFirst = this.shouldTextFirstBasedOnPosition(slide, textElements, images);

        if (textFirst) {
            contentContainer.appendChild(textArea);
            contentContainer.appendChild(imageArea);
        } else {
            contentContainer.appendChild(imageArea);
            contentContainer.appendChild(textArea);
        }
        
        // 组装完整布局：标题在上，内容在下
        if (titleElements.length > 0) {
            mainContainer.appendChild(titleArea);
        }
        mainContainer.appendChild(contentContainer);
        
        // 清空原始内容
        slide.innerHTML = '';
        
        // 添加新布局
        slide.appendChild(mainContainer);
    },
    
    // 根据原始位置关系判断文本是否应该放在左侧
    shouldTextFirstBasedOnPosition: function(slide, textElements, images) {
        if (textElements.length === 0 || images.length === 0) {
            return true;
        }

        // 获取所有原始子元素的位置信息
        const allChildren = Array.from(slide.children);

        // 找到第一个文本元素和第一个图片元素的索引（递归检查子节点）
        let firstTextIndex = -1;
        let firstImageIndex = -1;

        function containsImage(element) {
            if (element.tagName && element.tagName.toLowerCase() === 'img') return true;
            return !!element.querySelector && element.querySelector('img');
        }

        function isTextElement(element) {
            const tagName = element.tagName ? element.tagName.toLowerCase() : '';
            return !['img', 'script', 'style', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName) &&
                   element.textContent && element.textContent.trim() !== '';
        }

        for (let i = 0; i < allChildren.length; i++) {
            const element = allChildren[i];

            // 检查是否为文本元素
            if (firstTextIndex === -1 && isTextElement(element)) {
                firstTextIndex = i;
            }

            // 检查是否为图片元素或包含图片
            if (firstImageIndex === -1 && containsImage(element)) {
                firstImageIndex = i;
            }

            // 如果都找到了，提前退出循环
            if (firstTextIndex !== -1 && firstImageIndex !== -1) {
                break;
            }
        }

        // 如果文本在图片之前（上方），则文本放左侧，否则放右侧
        if (firstTextIndex !== -1 && firstImageIndex !== -1) {
            return firstTextIndex < firstImageIndex;
        }

        // 如果只有文本或只有图片，默认文本在左
        return firstTextIndex !== -1;
    },
    
    // 保留原有方法作为备用（现在已不使用）
    shouldTextFirst: function(textElements, images) {
        // 根据内容长度和图片数量决定
        const textLength = textElements.reduce((total, el) => 
            total + el.textContent.length, 0);
        const imageCount = images.length;
        
        // 如果文本较长或图片较多，文本放左侧
        return textLength > 200 || imageCount > 2;
    },
    
    // 公共方法：手动触发重排
    reprocess: function() {
        const slides = document.querySelectorAll('.reveal .slides section');
        slides.forEach(slide => {
            slide.classList.remove('smart-layout-processed');
            this.processSlide(slide);
        });
    },
    
    // 公共方法：禁用特定幻灯片的智能布局
    disable: function(slideSelector) {
        const slide = document.querySelector(slideSelector);
        if (slide) {
            slide.classList.add('smart-layout-disabled');
        }
    }
};

// 如果在浏览器环境中，自动注册插件
if (typeof window !== 'undefined' && window.Reveal) {
    window.Reveal.registerPlugin('smartlayout', SmartLayout);
}

// 导出插件（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartLayout;
}