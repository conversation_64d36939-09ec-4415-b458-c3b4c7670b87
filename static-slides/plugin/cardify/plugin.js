/**
 * Reveal.js Cardify Plugin
 * 将第一级 ul/ol 条目转换为横向排列的卡片网格，为单级列表提供列表风格的卡片效果。
 * 列表风格的卡片网格：将列表项转换为列表风格的卡片，保留列表结构。
 * 列表风格的卡片：将列表项转换为列表风格的卡片，不保留列表结构。
 */

const RevealCardify = {
  id: 'cardify',
  
  init: function(reveal) {
    // 添加样式
    this.addStyles();
    
    // 监听幻灯片变化事件
    reveal.on('slidechanged', this.transformSlide);
    reveal.on('ready', this.transformAllSlides);
  },
  
  // 添加必要的CSS样式
  addStyles: function() {
    const style = document.createElement('style');
    style.textContent = `
      /* 普通卡片网格样式 */
      .card-grid {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: flex-start;
        margin: 20px 0;
        row-gap: 20px;
      }
      
      .card-item {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 10px;
        padding-bottom: 6px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        flex: 1;
        min-width: 250px;
        margin: 0px 10px;
        color: inherit;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      
      .card-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.15);
      }
      
      .card-title {
        font-weight: bold;
        color: inherit;
        padding-bottom: 6px;
        line-height: 1.3;
        word-wrap: break-word;
        hyphens: auto;
        text-align: left;
      }
      
      .card-content {
        line-height: 1.6;
        word-wrap: break-word;
        hyphens: auto;
        border-top: 1px solid rgba(128, 128, 128, 0.3);
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
      
      .card-content ul {
        margin: 0;
        padding-left: 20px;
        list-style: none;
        padding-inline-start: 1em;
      }
      
      .card-content ol {
        margin: 0;
        padding-left: 20px;
        list-style: decimal;
        padding-inline-start: 1em;
      }
      
      .card-content li {
        margin: 4px 0;
        position: relative;
      }
      
      .card-content ul li {
        list-style: none;
      }
      
      .card-content ul li:before {
        content: "▸";
        color: rgba(128, 128, 128, 0.7);
        margin-right: 8px;
        font-weight: bold;
        position: absolute;
        left: -15px;
      }
      
      .card-content ol li {
        list-style: decimal;
        list-style-position: outside;
      }
      
      /* 列表风格的卡片网格样式 */
      .list-card-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin: 10px 10px;
        padding: 0;
        list-style: none;
      }
      
      .list-card-item {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 9px 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        color: inherit;
        transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
        position: relative;
        overflow: hidden;
        min-height: auto;
        display: flex;
        align-items: flex-start;
        font-size: inherit;
        line-height: 1.2;
        font-size: x-large;
      }
      
      .list-card-item:hover {
        transform: translateX(5px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.08);
      }
      
      /* 为 ul 类型的列表添加三角形前缀 */
      .list-card-grid.ul-style .list-card-item:before {
        content: "▶";
        color: rgba(128, 128, 128, 0.8);
        margin-right: 10px;
        margin-top: 2px;
        font-weight: bold;
        flex-shrink: 0;
        font-size: 0.8em;
        line-height: 1.2;
      }
      
      /* 为 ol 类型的列表添加序号前缀 */
      .list-card-grid.ol-style {
        counter-reset: list-counter;
      }
      
      .list-card-grid.ol-style .list-card-item {
        counter-increment: list-counter;
      }
      
      .list-card-grid.ol-style .list-card-item:before {
        content: counter(list-counter) ".";
        color: rgba(128, 128, 128, 0.8);
        font-weight: bold;
        flex-shrink: 0;
        margin-right: 10px;
        margin-top: 2px;
        min-width: 25px;
        text-align: left;
        font-size: 0.8em;
        line-height: 1.2;
      }
      
      .list-card-content {
        flex: 1;
        word-wrap: break-word;
        hyphens: auto;
        text-align: left;
      }
      
      /* 动态字体大小类 */
      .card-grid.size-small .card-title {
        font-size: 0.9rem;
      }
      
      .card-grid.size-small .card-content {
        font-size: 0.8rem;
      }
      
      .card-grid.size-medium .card-title {
        font-size: 1.1rem;
      }
      
      .card-grid.size-medium .card-content {
        font-size: 0.9rem;
      }
      
      .card-grid.size-large .card-title {
        font-size: 1.3rem;
      }
      
      .card-grid.size-large .card-content {
        font-size: 1.0rem;
      }
      
      .card-grid.size-xlarge .card-title {
        font-size: 1.5rem;
      }
      
      .card-grid.size-xlarge .card-content {
        font-size: 1.1rem;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .card-grid {
          flex-direction: column;
          padding: 1em;
          gap: 1em;
        }
        
        .card-item {
          min-width: 93%;
          max-width: 93%;
          padding: 1em;
        }
        
        .list-card-grid {
            gap: 1em;
            margin: 1em;
        }
            
        .list-card-item {
          padding: 0.5em;
        }

        .card-grid.size-small .card-title {
            font-size: 1.1em;
        }
        
        .card-grid.size-small .card-content {
            font-size: 1.0em;
        }
        
        .card-grid.size-medium .card-title {
            font-size: 1.3em;
        }
        
        .card-grid.size-medium .card-content {
            font-size: 1.2em;
        }
        
        .card-grid.size-large .card-title {
            font-size: 1.5em;
        }
        
        .card-grid.size-large .card-content {
            font-size: 1.3em;
        }
        
        .card-grid.size-xlarge .card-title {
            font-size: 1.6em;
        }
        
        .card-grid.size-xlarge .card-content {
            font-size: 1.3em;
        }

        .list-card-item {
            font-size: 1.5em;
        }

        .reveal h1 {
            font-size: 2em;
        }

        .reveal h2 {
            font-size: 1.8em;
        }

        .reveal h3 {
            font-size: 1.5em;        
        }

        .reveal h4 {
            font-size: 1.3em;
        }

        .reveal p {
            font-size: 1.4em;
        }

        .card-content ul li:before {
            left: -1em;
        }
      }
    `;
    document.head.appendChild(style);
  },
  
  // 转换所有幻灯片
  transformAllSlides: function() {
    const slides = document.querySelectorAll('.reveal .slides section');
    slides.forEach(slide => {
      RevealCardify.transformSlideElement(slide);
    });
  },
  
  // 转换当前幻灯片
  transformSlide: function(event) {
    RevealCardify.transformSlideElement(event.currentSlide);
  },
  
  // 转换幻灯片元素
  transformSlideElement: function(slide) {
    if (!slide) return;

    if(slide.querySelector('img')) {
        return;
    }
    
    // 查找所有第一级 ul/ol 元素（不在其他 ul/ol 内部的）
    const topLevelLists = slide.querySelectorAll('ul:not(ul ul, ol ul, .card-content ul), ol:not(ul ol, ol ol, .card-content ol)');
    
    topLevelLists.forEach(list => {
      // 跳过已经转换过的
      if (list.classList.contains('card-grid-processed')) return;
      
      // 获取所有第一级 li 元素
      const topLevelLis = Array.from(list.children).filter(child => 
        child.tagName.toLowerCase() === 'li'
      );
      
      if (topLevelLis.length === 0) return;
      
      // 检查是否只有一级列表（所有li都没有子ul/ol）
      const hasNestedLists = topLevelLis.some(li => 
        li.querySelector('ul, ol') !== null
      );
      
      // 根据是否有嵌套列表决定处理方式
      if (hasNestedLists) {
        // 有嵌套列表，创建普通卡片网格
        this.createCardGrid(list, topLevelLis);
      } else {
        // 只有一级列表，创建列表风格的卡片
        this.createListCardGrid(list, topLevelLis);
      }
      
      // 标记为已处理
      list.classList.add('card-grid-processed');
    });
  },
  
  // 创建普通卡片网格（用于有嵌套列表的情况）
  createCardGrid: function(list, topLevelLis) {
    // 创建卡片网格容器
    const cardGrid = document.createElement('div');
    cardGrid.className = 'card-grid';
    
    topLevelLis.forEach(li => {
      const card = this.createCard(li);
      cardGrid.appendChild(card);
    });
    
    // 应用动态字体大小
    this.applyDynamicSizing(cardGrid, topLevelLis);
    
    // 替换原来的列表
    list.parentNode.replaceChild(cardGrid, list);
    
    // 标记为已处理
    cardGrid.classList.add('card-grid-processed');
  },
  
  // 创建列表风格的卡片网格（用于只有一级li的情况）
  createListCardGrid: function(list, topLevelLis) {
    // 创建列表卡片网格容器
    const listCardGrid = document.createElement('div');
    listCardGrid.className = 'list-card-grid';
    
    // 根据原始列表类型添加样式类
    const isOrderedList = list.tagName.toLowerCase() === 'ol';
    if (isOrderedList) {
      listCardGrid.classList.add('ol-style');
    } else {
      listCardGrid.classList.add('ul-style');
    }
    
    topLevelLis.forEach(li => {
      const listCard = this.createListCard(li);
      listCardGrid.appendChild(listCard);
    });
    
    // 替换原来的列表
    list.parentNode.replaceChild(listCardGrid, list);
    
    // 标记为已处理
    listCardGrid.classList.add('card-grid-processed');
  },
  
  // 创建单个普通卡片
  createCard: function(li) {
    const card = document.createElement('div');
    card.className = 'card-item';
    
    // 提取第一级文本作为标题
    const title = this.extractTitle(li);
    
    // 创建标题元素
    const titleElement = document.createElement('div');
    titleElement.className = 'card-title';
    titleElement.textContent = title;
    card.appendChild(titleElement);
    
    // 创建内容容器
    const contentElement = document.createElement('div');
    contentElement.className = 'card-content';
    
    // 提取嵌套内容
    const nestedContent = this.extractNestedContent(li);
    if (nestedContent) {
      contentElement.appendChild(nestedContent);
      card.appendChild(contentElement);
    }
    
    return card;
  },
  
  // 创建单个列表卡片
  createListCard: function(li) {
    const listCard = document.createElement('div');
    listCard.className = 'list-card-item';
    
    // 创建内容容器
    const contentElement = document.createElement('div');
    contentElement.className = 'list-card-content';
    contentElement.textContent = li.textContent.trim();
    
    listCard.appendChild(contentElement);
    
    return listCard;
  },
  
  // 提取第一级文本作为标题
  extractTitle: function(li) {
    const clone = li.cloneNode(true);
    
    // 移除所有嵌套的 ul/ol 元素
    const nestedLists = clone.querySelectorAll('ul, ol');
    nestedLists.forEach(list => list.remove());
    
    // 获取剩余的文本内容
    return clone.textContent.trim() || '无标题';
  },
  
  // 提取嵌套内容（修复：只提取直接子级列表）
  extractNestedContent: function(li) {
    const container = document.createElement('div');
    let hasContent = false;
    
    // 只选择直接子级的 ul/ol 元素，而不是所有嵌套的
    const directChildLists = Array.from(li.children).filter(child => 
      child.tagName.toLowerCase() === 'ul' || child.tagName.toLowerCase() === 'ol'
    );
    
    if (directChildLists.length === 0) return null;
    
    directChildLists.forEach(list => {
      const clonedList = list.cloneNode(true);
      container.appendChild(clonedList);
      hasContent = true;
    });
    
    return hasContent ? container : null;
  },
  
  // 根据内容量和屏幕大小动态调整字体
  applyDynamicSizing: function(cardGrid, listItems) {
    const cardCount = listItems.length;
    const slideWidth = window.innerWidth || document.documentElement.clientWidth;
    const slideHeight = window.innerHeight || document.documentElement.clientHeight;
    
    // 计算总内容量（标题长度 + 子项数量）
    let totalContentLength = 0;
    let totalSubItems = 0;
    
    listItems.forEach(li => {
      const title = this.extractTitle(li);
      totalContentLength += title.length;
      
      const subItems = li.querySelectorAll('ul li, ol li');
      totalSubItems += subItems.length;
      
      subItems.forEach(subItem => {
        totalContentLength += subItem.textContent.trim().length;
      });
    });
    
    // 计算平均内容密度
    const avgContentPerCard = totalContentLength / cardCount;
    const avgSubItemsPerCard = totalSubItems / cardCount;
    
    // 根据屏幕尺寸和内容量决定大小等级
    let sizeClass = 'size-medium'; // 默认
    
    // 屏幕较小时优先考虑内容适配
    if (slideWidth < 768) {
      if (avgContentPerCard > 100 || avgSubItemsPerCard > 5) {
        sizeClass = 'size-small';
      } else {
        sizeClass = 'size-medium';
      }
    }
    // 中等屏幕
    else if (slideWidth < 1200) {
      if (avgContentPerCard > 150 || avgSubItemsPerCard > 7) {
        sizeClass = 'size-small';
      } else if (avgContentPerCard < 50 && avgSubItemsPerCard < 3) {
        sizeClass = 'size-large';
      } else {
        sizeClass = 'size-medium';
      }
    }
    // 大屏幕
    else {
      if (avgContentPerCard > 200 || avgSubItemsPerCard > 8) {
        sizeClass = 'size-small';
      } else if (avgContentPerCard < 30 && avgSubItemsPerCard < 2) {
        sizeClass = 'size-xlarge';
      } else if (avgContentPerCard < 80 && avgSubItemsPerCard < 4) {
        sizeClass = 'size-large';
      } else {
        sizeClass = 'size-medium';
      }
    }
    
    // 考虑卡片数量的影响
    if (cardCount >= 5) {
      // 卡片太多时，减小字体
      sizeClass = sizeClass === 'size-xlarge' ? 'size-large' : 
                  sizeClass === 'size-large' ? 'size-medium' : 
                  sizeClass === 'size-medium' ? 'size-small' : 'size-small';
    } else if (cardCount <= 2) {
      // 卡片较少时，可以适当增大字体
      sizeClass = sizeClass === 'size-small' ? 'size-medium' : 
                  sizeClass === 'size-medium' ? 'size-large' : 
                  sizeClass === 'size-large' ? 'size-xlarge' : 'size-xlarge';
    }
    
    cardGrid.classList.add(sizeClass);
  }
};

// 注册插件
if (window.Reveal) {
  window.Reveal.registerPlugin('cardify', RevealCardify);
} else {
  // 如果 Reveal 还没有加载，等待其加载
  window.addEventListener('load', function() {
    if (window.Reveal) {
      window.Reveal.registerPlugin('cardify', RevealCardify);
    }
  });
}

// 导出插件（支持模块化）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RevealCardify;
}