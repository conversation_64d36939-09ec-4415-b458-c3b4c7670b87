/**
 * Reveal.js Cardify Plugin with SmartLayout Integration
 * 将第一级 ul/ol 条目转换为横向排列的卡片网格，为单级列表提供列表风格的卡片效果。
 * 列表风格的卡片网格：将列表项转换为列表风格的卡片，保留列表结构。
 * 列表风格的卡片：将列表项转换为列表风格的卡片，不保留列表结构。
 * 整合了 SmartLayout 功能：自动检测并重新排列幻灯片中的图片和文本内容，实现图文混排。
 */

const RevealCardify = {
  id: 'cardify',

  init: function(reveal) {
    // 添加样式（包含 SmartLayout 样式）
    this.addStyles();

    // 监听幻灯片变化事件
    reveal.on('slidechanged', this.transformSlide);
    reveal.on('ready', this.transformAllSlides);
  },
  
  // 添加必要的CSS样式（包含 SmartLayout 样式）
  addStyles: function() {
    const style = document.createElement('style');
    style.textContent = `
      /* SmartLayout 样式 - 主容器 */
      .smart-layout-main {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
      }

      /* SmartLayout 样式 - 标题区域 */
      .smart-layout-title {
        flex-shrink: 0;
        margin-bottom: 1.5rem;
        text-align: center;
      }

      .smart-layout-title h1,
      .smart-layout-title h2,
      .smart-layout-title h3,
      .smart-layout-title h4,
      .smart-layout-title h5,
      .smart-layout-title h6 {
        margin-top: 0;
        margin-bottom: 0.5rem;
      }

      /* SmartLayout 样式 - 智能布局容器 */
      .smart-layout-container {
        display: flex;
        align-items: flex-start;
        gap: 2rem;
        flex: 1;
        min-height: 0;
      }

      /* SmartLayout 样式 - 文本区域（整合卡片样式） */
      .smart-layout-text {
        flex: 1;
        min-width: 40%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        /* 卡片样式 */
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        color: inherit;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .smart-layout-text:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.15);
      }

      /* SmartLayout 样式 - 图片区域 */
      .smart-layout-images {
        flex: 1;
        min-width: 40%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
      }

      /* SmartLayout 样式 - 图片样式优化 */
      .smart-layout-images img {
        max-width: 100%;
        max-height: 70vh;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }

      /* SmartLayout 样式 - 多图片布局 */
      .smart-layout-images.multiple-images {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
      }

      .smart-layout-images.multiple-images img {
        max-height: 30vh;
      }

      /* SmartLayout 样式 - 保持原有样式的文本元素 */
      .smart-layout-text h1,
      .smart-layout-text h2,
      .smart-layout-text h3,
      .smart-layout-text h4,
      .smart-layout-text h5,
      .smart-layout-text h6 {
        margin-top: 0;
      }

      /* SmartLayout 样式 - 列表优化 */
      .smart-layout-text ul,
      .smart-layout-text ol {
        padding-left: 1.5rem;
      }

      /* 普通卡片网格样式 */
      .card-grid {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: flex-start;
        margin: 20px 0;
        row-gap: 20px;
      }
      
      .card-item {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 10px;
        padding-bottom: 6px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        flex: 1;
        min-width: 250px;
        margin: 0px 10px;
        color: inherit;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      
      .card-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.15);
      }
      
      .card-title {
        font-weight: bold;
        color: inherit;
        padding-bottom: 6px;
        line-height: 1.3;
        word-wrap: break-word;
        hyphens: auto;
        text-align: left;
      }
      
      .card-content {
        line-height: 1.6;
        word-wrap: break-word;
        hyphens: auto;
        border-top: 1px solid rgba(128, 128, 128, 0.3);
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
      
      .card-content ul {
        margin: 0;
        padding-left: 20px;
        list-style: none;
        padding-inline-start: 1em;
      }
      
      .card-content ol {
        margin: 0;
        padding-left: 20px;
        list-style: decimal;
        padding-inline-start: 1em;
      }
      
      .card-content li {
        margin: 4px 0;
        position: relative;
      }
      
      .card-content ul li {
        list-style: none;
      }
      
      .card-content ul li:before {
        content: "▸";
        color: rgba(128, 128, 128, 0.7);
        margin-right: 8px;
        font-weight: bold;
        position: absolute;
        left: -15px;
      }
      
      .card-content ol li {
        list-style: decimal;
        list-style-position: outside;
      }
      
      /* 列表风格的卡片网格样式 */
      .list-card-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin: 10px 10px;
        padding: 0;
        list-style: none;
      }
      
      .list-card-item {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 9px 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        color: inherit;
        transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
        position: relative;
        overflow: hidden;
        min-height: auto;
        display: flex;
        align-items: flex-start;
        font-size: inherit;
        line-height: 1.2;
        font-size: x-large;
      }
      
      .list-card-item:hover {
        transform: translateX(5px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.08);
      }
      
      /* 为 ul 类型的列表添加三角形前缀 */
      .list-card-grid.ul-style .list-card-item:before {
        content: "▶";
        color: rgba(128, 128, 128, 0.8);
        margin-right: 10px;
        margin-top: 2px;
        font-weight: bold;
        flex-shrink: 0;
        font-size: 0.8em;
        line-height: 1.2;
      }
      
      /* 为 ol 类型的列表添加序号前缀 */
      .list-card-grid.ol-style {
        counter-reset: list-counter;
      }
      
      .list-card-grid.ol-style .list-card-item {
        counter-increment: list-counter;
      }
      
      .list-card-grid.ol-style .list-card-item:before {
        content: counter(list-counter) ".";
        color: rgba(128, 128, 128, 0.8);
        font-weight: bold;
        flex-shrink: 0;
        margin-right: 10px;
        margin-top: 2px;
        min-width: 25px;
        text-align: left;
        font-size: 0.8em;
        line-height: 1.2;
      }
      
      .list-card-content {
        flex: 1;
        word-wrap: break-word;
        hyphens: auto;
        text-align: left;
      }
      
      /* 动态字体大小类 */
      .card-grid.size-small .card-title {
        font-size: 0.9rem;
      }
      
      .card-grid.size-small .card-content {
        font-size: 0.8rem;
      }
      
      .card-grid.size-medium .card-title {
        font-size: 1.1rem;
      }
      
      .card-grid.size-medium .card-content {
        font-size: 0.9rem;
      }
      
      .card-grid.size-large .card-title {
        font-size: 1.3rem;
      }
      
      .card-grid.size-large .card-content {
        font-size: 1.0rem;
      }
      
      .card-grid.size-xlarge .card-title {
        font-size: 1.5rem;
      }
      
      .card-grid.size-xlarge .card-content {
        font-size: 1.1rem;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        /* SmartLayout 响应式调整 */
        .smart-layout-container {
          flex-direction: column;
          gap: 1rem;
        }

        .smart-layout-text,
        .smart-layout-images {
          min-width: 100%;
        }

        .smart-layout-images img {
          max-height: 40vh;
        }

        .card-grid {
          flex-direction: column;
          padding: 1em;
          gap: 1em;
        }

        .card-item {
          min-width: 93%;
          max-width: 93%;
          padding: 1em;
        }

        .list-card-grid {
            gap: 1em;
            margin: 1em;
        }

        .list-card-item {
          padding: 0.5em;
        }

        .card-grid.size-small .card-title {
            font-size: 1.1em;
        }
        
        .card-grid.size-small .card-content {
            font-size: 1.0em;
        }
        
        .card-grid.size-medium .card-title {
            font-size: 1.3em;
        }
        
        .card-grid.size-medium .card-content {
            font-size: 1.2em;
        }
        
        .card-grid.size-large .card-title {
            font-size: 1.5em;
        }
        
        .card-grid.size-large .card-content {
            font-size: 1.3em;
        }
        
        .card-grid.size-xlarge .card-title {
            font-size: 1.6em;
        }
        
        .card-grid.size-xlarge .card-content {
            font-size: 1.3em;
        }

        .list-card-item {
            font-size: 1.5em;
        }

        .reveal h1 {
            font-size: 2em;
        }

        .reveal h2 {
            font-size: 1.8em;
        }

        .reveal h3 {
            font-size: 1.5em;        
        }

        .reveal h4 {
            font-size: 1.3em;
        }

        .reveal p {
            font-size: 1.4em;
        }

        .card-content ul li:before {
            left: -1em;
        }
      }
    `;
    document.head.appendChild(style);
  },
  
  // 转换所有幻灯片
  transformAllSlides: function() {
    const slides = document.querySelectorAll('.reveal .slides section');
    slides.forEach(slide => {
      RevealCardify.transformSlideElement(slide);
    });
  },
  
  // 转换当前幻灯片
  transformSlide: function(event) {
    RevealCardify.transformSlideElement(event.currentSlide);
  },
  
  // 转换幻灯片元素（整合 SmartLayout 功能）
  transformSlideElement: function(slide) {
    if (!slide) return;

    // 跳过已处理的幻灯片
    if (slide.classList.contains('smart-layout-processed') || slide.classList.contains('card-grid-processed')) {
      return;
    }

    // 查找标题、图片和文本元素
    const titleElements = this.getTitleElements(slide);
    const images = slide.querySelectorAll('img');
    const textElements = this.getTextElements(slide);

    // 如果同时存在图片和文本，进行图文混排
    if (images.length > 0 && textElements.length > 0) {
      this.rearrangeContentWithCards(slide, images, textElements, titleElements);
      slide.classList.add('smart-layout-processed');
      return;
    }

    // 如果只有文本内容，进行普通的卡片化处理
    // 查找所有第一级 ul/ol 元素（不在其他 ul/ol 内部的）
    const topLevelLists = slide.querySelectorAll('ul:not(ul ul, ol ul, .card-content ul), ol:not(ul ol, ol ol, .card-content ol)');

    topLevelLists.forEach(list => {
      // 跳过已经转换过的
      if (list.classList.contains('card-grid-processed')) return;

      // 获取所有第一级 li 元素
      const topLevelLis = Array.from(list.children).filter(child =>
        child.tagName.toLowerCase() === 'li'
      );

      if (topLevelLis.length === 0) return;

      // 检查是否只有一级列表（所有li都没有子ul/ol）
      const hasNestedLists = topLevelLis.some(li =>
        li.querySelector('ul, ol') !== null
      );

      // 根据是否有嵌套列表决定处理方式
      if (hasNestedLists) {
        // 有嵌套列表，创建普通卡片网格
        this.createCardGrid(list, topLevelLis);
      } else {
        // 只有一级列表，创建列表风格的卡片
        this.createListCardGrid(list, topLevelLis);
      }

      // 标记为已处理
      list.classList.add('card-grid-processed');
    });

    // 标记为已处理
    slide.classList.add('card-grid-processed');
  },
  
  // 创建普通卡片网格（用于有嵌套列表的情况）
  createCardGrid: function(list, topLevelLis) {
    // 创建卡片网格容器
    const cardGrid = document.createElement('div');
    cardGrid.className = 'card-grid';
    
    topLevelLis.forEach(li => {
      const card = this.createCard(li);
      cardGrid.appendChild(card);
    });
    
    // 应用动态字体大小
    this.applyDynamicSizing(cardGrid, topLevelLis);
    
    // 替换原来的列表
    list.parentNode.replaceChild(cardGrid, list);
    
    // 标记为已处理
    cardGrid.classList.add('card-grid-processed');
  },
  
  // 创建列表风格的卡片网格（用于只有一级li的情况）
  createListCardGrid: function(list, topLevelLis) {
    // 创建列表卡片网格容器
    const listCardGrid = document.createElement('div');
    listCardGrid.className = 'list-card-grid';
    
    // 根据原始列表类型添加样式类
    const isOrderedList = list.tagName.toLowerCase() === 'ol';
    if (isOrderedList) {
      listCardGrid.classList.add('ol-style');
    } else {
      listCardGrid.classList.add('ul-style');
    }
    
    topLevelLis.forEach(li => {
      const listCard = this.createListCard(li);
      listCardGrid.appendChild(listCard);
    });
    
    // 替换原来的列表
    list.parentNode.replaceChild(listCardGrid, list);
    
    // 标记为已处理
    listCardGrid.classList.add('card-grid-processed');
  },
  
  // 创建单个普通卡片
  createCard: function(li) {
    const card = document.createElement('div');
    card.className = 'card-item';
    
    // 提取第一级文本作为标题
    const title = this.extractTitle(li);
    
    // 创建标题元素
    const titleElement = document.createElement('div');
    titleElement.className = 'card-title';
    titleElement.textContent = title;
    card.appendChild(titleElement);
    
    // 创建内容容器
    const contentElement = document.createElement('div');
    contentElement.className = 'card-content';
    
    // 提取嵌套内容
    const nestedContent = this.extractNestedContent(li);
    if (nestedContent) {
      contentElement.appendChild(nestedContent);
      card.appendChild(contentElement);
    }
    
    return card;
  },
  
  // 创建单个列表卡片
  createListCard: function(li) {
    const listCard = document.createElement('div');
    listCard.className = 'list-card-item';
    
    // 创建内容容器
    const contentElement = document.createElement('div');
    contentElement.className = 'list-card-content';
    contentElement.textContent = li.textContent.trim();
    
    listCard.appendChild(contentElement);
    
    return listCard;
  },
  
  // 提取第一级文本作为标题
  extractTitle: function(li) {
    const clone = li.cloneNode(true);
    
    // 移除所有嵌套的 ul/ol 元素
    const nestedLists = clone.querySelectorAll('ul, ol');
    nestedLists.forEach(list => list.remove());
    
    // 获取剩余的文本内容
    return clone.textContent.trim() || '无标题';
  },
  
  // 提取嵌套内容（修复：只提取直接子级列表）
  extractNestedContent: function(li) {
    const container = document.createElement('div');
    let hasContent = false;
    
    // 只选择直接子级的 ul/ol 元素，而不是所有嵌套的
    const directChildLists = Array.from(li.children).filter(child => 
      child.tagName.toLowerCase() === 'ul' || child.tagName.toLowerCase() === 'ol'
    );
    
    if (directChildLists.length === 0) return null;
    
    directChildLists.forEach(list => {
      const clonedList = list.cloneNode(true);
      container.appendChild(clonedList);
      hasContent = true;
    });
    
    return hasContent ? container : null;
  },
  
  // 根据内容量和屏幕大小动态调整字体
  applyDynamicSizing: function(cardGrid, listItems) {
    const cardCount = listItems.length;
    const slideWidth = window.innerWidth || document.documentElement.clientWidth;
    
    // 计算总内容量（标题长度 + 子项数量）
    let totalContentLength = 0;
    let totalSubItems = 0;
    
    listItems.forEach(li => {
      const title = this.extractTitle(li);
      totalContentLength += title.length;
      
      const subItems = li.querySelectorAll('ul li, ol li');
      totalSubItems += subItems.length;
      
      subItems.forEach(subItem => {
        totalContentLength += subItem.textContent.trim().length;
      });
    });
    
    // 计算平均内容密度
    const avgContentPerCard = totalContentLength / cardCount;
    const avgSubItemsPerCard = totalSubItems / cardCount;
    
    // 根据屏幕尺寸和内容量决定大小等级
    let sizeClass = 'size-medium'; // 默认
    
    // 屏幕较小时优先考虑内容适配
    if (slideWidth < 768) {
      if (avgContentPerCard > 100 || avgSubItemsPerCard > 5) {
        sizeClass = 'size-small';
      } else {
        sizeClass = 'size-medium';
      }
    }
    // 中等屏幕
    else if (slideWidth < 1200) {
      if (avgContentPerCard > 150 || avgSubItemsPerCard > 7) {
        sizeClass = 'size-small';
      } else if (avgContentPerCard < 50 && avgSubItemsPerCard < 3) {
        sizeClass = 'size-large';
      } else {
        sizeClass = 'size-medium';
      }
    }
    // 大屏幕
    else {
      if (avgContentPerCard > 200 || avgSubItemsPerCard > 8) {
        sizeClass = 'size-small';
      } else if (avgContentPerCard < 30 && avgSubItemsPerCard < 2) {
        sizeClass = 'size-xlarge';
      } else if (avgContentPerCard < 80 && avgSubItemsPerCard < 4) {
        sizeClass = 'size-large';
      } else {
        sizeClass = 'size-medium';
      }
    }
    
    // 考虑卡片数量的影响
    if (cardCount >= 5) {
      // 卡片太多时，减小字体
      sizeClass = sizeClass === 'size-xlarge' ? 'size-large' : 
                  sizeClass === 'size-large' ? 'size-medium' : 
                  sizeClass === 'size-medium' ? 'size-small' : 'size-small';
    } else if (cardCount <= 2) {
      // 卡片较少时，可以适当增大字体
      sizeClass = sizeClass === 'size-small' ? 'size-medium' : 
                  sizeClass === 'size-medium' ? 'size-large' : 
                  sizeClass === 'size-large' ? 'size-xlarge' : 'size-xlarge';
    }
    
    cardGrid.classList.add(sizeClass);
  },

  // ========== SmartLayout 相关方法 ==========

  // 获取标题元素
  getTitleElements: function(slide) {
    return Array.from(slide.children).filter(element => {
      const tagName = element.tagName.toLowerCase();
      return ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName);
    });
  },

  // 获取文本元素（排除图片和标题）
  getTextElements: function(slide) {
    const allElements = Array.from(slide.children);
    return allElements.filter(element => {
      // 排除script、style、img、标题等元素
      const tagName = element.tagName.toLowerCase();
      return !['img', 'script', 'style', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName) &&
             element.textContent.trim() !== '';
    });
  },

  // 重新排列内容（整合卡片样式）
  rearrangeContentWithCards: function(slide, images, textElements, titleElements) {
    // 创建主容器来包含标题和内容区域
    const mainContainer = document.createElement('div');
    mainContainer.className = 'smart-layout-main';

    // 创建标题区域
    const titleArea = document.createElement('div');
    titleArea.className = 'smart-layout-title';

    // 创建智能布局容器（用于图文内容）
    const contentContainer = document.createElement('div');
    contentContainer.className = 'smart-layout-container';

    // 创建文本区域（已经包含卡片样式）
    const textArea = document.createElement('div');
    textArea.className = 'smart-layout-text';

    // 创建图片区域
    const imageArea = document.createElement('div');
    imageArea.className = 'smart-layout-images';

    // 如果有多个图片，使用网格布局
    if (images.length > 1) {
      imageArea.classList.add('multiple-images');
    }

    // 移动标题元素到标题区域
    titleElements.forEach(element => {
      titleArea.appendChild(element.cloneNode(true));
    });

    // 移动文本元素到文本区域
    textElements.forEach(element => {
      textArea.appendChild(element.cloneNode(true));
    });

    // 移动图片到图片区域
    images.forEach(img => {
      if (!img.hasAttribute('data-preview-image')) {
        img.setAttribute('data-preview-image', ''); // 设置你想要的默认值
      }
      imageArea.appendChild(img.cloneNode(true));
    });

    // 根据原始位置关系决定布局方向
    const textFirst = this.shouldTextFirstBasedOnPosition(slide, textElements, images);

    if (textFirst) {
      contentContainer.appendChild(textArea);
      contentContainer.appendChild(imageArea);
    } else {
      contentContainer.appendChild(imageArea);
      contentContainer.appendChild(textArea);
    }

    // 组装完整布局：标题在上，内容在下
    if (titleElements.length > 0) {
      mainContainer.appendChild(titleArea);
    }
    mainContainer.appendChild(contentContainer);

    // 清空原始内容
    slide.innerHTML = '';

    // 添加新布局
    slide.appendChild(mainContainer);
  },

  // 根据原始位置关系判断文本是否应该放在左侧
  shouldTextFirstBasedOnPosition: function(slide, textElements, images) {
    if (textElements.length === 0 || images.length === 0) {
      return true;
    }

    // 获取所有原始子元素的位置信息
    const allChildren = Array.from(slide.children);

    // 找到第一个文本元素和第一个图片元素的索引（递归检查子节点）
    let firstTextIndex = -1;
    let firstImageIndex = -1;

    function containsImage(element) {
      if (element.tagName && element.tagName.toLowerCase() === 'img') return true;
      return !!element.querySelector && element.querySelector('img');
    }

    function isTextElement(element) {
      const tagName = element.tagName ? element.tagName.toLowerCase() : '';
      return !['img', 'script', 'style', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName) &&
             element.textContent && element.textContent.trim() !== '';
    }

    for (let i = 0; i < allChildren.length; i++) {
      const element = allChildren[i];

      // 检查是否为文本元素
      if (firstTextIndex === -1 && isTextElement(element)) {
        firstTextIndex = i;
      }

      // 检查是否为图片元素或包含图片
      if (firstImageIndex === -1 && containsImage(element)) {
        firstImageIndex = i;
      }

      // 如果都找到了，提前退出循环
      if (firstTextIndex !== -1 && firstImageIndex !== -1) {
        break;
      }
    }

    // 如果文本在图片之前（上方），则文本放左侧，否则放右侧
    if (firstTextIndex !== -1 && firstImageIndex !== -1) {
      return firstTextIndex < firstImageIndex;
    }

    // 如果只有文本或只有图片，默认文本在左
    return firstTextIndex !== -1;
  }
};

// 注册插件
if (window.Reveal) {
  window.Reveal.registerPlugin('cardify', RevealCardify);
} else {
  // 如果 Reveal 还没有加载，等待其加载
  window.addEventListener('load', function() {
    if (window.Reveal) {
      window.Reveal.registerPlugin('cardify', RevealCardify);
    }
  });
}

// 导出插件（支持模块化）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RevealCardify;
}