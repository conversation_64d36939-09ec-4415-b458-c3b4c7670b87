var server_host = '';
const debugLog = false;

function isPreview() {
  return !!window.location.search.match(/preview/gi);
}

var mode = getUrlParameter("mode");
var tts_native = getUrlParameter("tts_native");

var showNotes = getUrlParameter("showNotes") == 'true';
var toggleCaption = getUrlParameter("showCaption") != 'false';
var zoom = parseFloat(getUrlParameter('zoom')) || 100;
var aspectRatio = getUrlParameter('aspectRatio') || 'sixteen9';
var ratios = {
  sixteen9: {
    width: 16,
    height: 9
  },
  four3: {
    width: 4,
    height: 3
  },
  three4: {
    width: 3,
    height: 4
  },
  nine16: {
    width: 9,
    height: 16
  },
  nineteen9: {
    width: 19,
    height: 9
  },
  sixteen10: {
    width: 16,
    height: 10
  }
}

const computeSize = () => {
  let width = 960, height;
  let ratio = ratios[aspectRatio];
  if (ratio.width / ratio.height < 1) {
    width = 600;
  }
  height = width * ratio.height / ratio.width;

  return { width, height }
}

var revealInited = false;
function initializeReveal(showNotes = false) {
  // Full list of configuration options available at:
  // https://github.com/hakimel/reveal.js#configuration
  if (revealInited) {
    return;
  }
  var plugins = [RevealCardify, RevealZoom, RevealNotes, RevealSearch, RevealMath.KaTeX, RevealChart, RevealMarkdown, RevealFullscreen, RevealVideoRender, RevealHighlight
    // , RevealLayout
  ];
  // if (mode === 'article') {
  //   plugins.push(RevealToArticle);
  // }

  // const { width, height } = computeSize();
  Reveal.initialize({
    controls: getUrlParameter("recording") !== 'true',
    controlsTutorial: true,
    progress: true,
    center: mode !== 'article',
    hash: true,
    showNotes: showNotes,
    margin: 0,
    mode,
    // width,
    // height,
    postMessageEvent: false,
    // Learn about plugins: https://revealjs.com/plugins/
    plugins,
    katex: {
      local: 'lib/katex'
    },
    chart: {
      defaults: {
        color: 'gray', // color of labels
        scale: {
          beginAtZero: true,
          ticks: { stepSize: 1 },
          grid: { color: "lightgray" }, // color of grid lines
        },
      },
      line: { borderColor: chart_colors },
      bar: { backgroundColor: chart_colors },
      pie: { backgroundColor: [chart_colors] },
    },
  }).then(() => {
    revealInited = true;
    Reveal.getPlugin('katex').init(Reveal);
    sendMessageToParent({type: 'revealinited'})
  });
}

function reInitializeReveal(showNotes) {
  revealInited = false;
  initializeReveal(showNotes)
}

function sendMessageToParent(message) {
  try {
    window.parent && window.parent.postMessage(JSON.stringify(message), '*');
  } catch (err) {
    console.log(err);
  }
}

function highlightAnyCodeBlocks() {
  $(document).ready(function () {
    $('pre code').each(function (i, block) {
      hljs.highlightBlock(block);
    });
  });
}

let hid = getUrlParameter("hid", (mode == 'edit' || mode == 'present') && window.parent.location.search.substring(1));
const doctype = getUrlParameter('doctype');

function insertMarkdownReference() {
  var markdownReference;

  let markdownUrl = `${server_host}/slides/${doctype === 'doc' ? 'serializeDocToSlides/' : ''}hid/${hid}`;
  let pageName = getUrlPageName((mode == 'edit' || mode == 'present') && window.parent.location.search.substring(1));

  if (pageName === 'present.html' || mode == 'present') {
    markdownUrl += '?mode=present';
  } else {
    markdownUrl += '?mode=view';
  }

  let warzone = getUrlParameter("warzone");
  if (warzone && warzone != 'undefined') {
    markdownUrl += `&warzone=${warzone}`;
  }

  markdownReference = $('<section/>', {
    'data-markdown': markdownUrl,
    'data-separator': "^--- *$",
    'data-separator-vertical': "^-- *$",
    'data-separator-notes': "^Notes?:",
    'data-charset': "utf-8"
  });


  $('.slides').html(markdownReference);

  // if (mode === 'present') {
  //   let bodyStyle = document.querySelector('body').style;
  //   bodyStyle.display = 'flex';
  //   bodyStyle.justifyContent = 'center';
  //   bodyStyle.alignItems = 'center';
  // }

  // let ratio = ratios[aspectRatio];

  // if (window.innerWidth / window.innerHeight > ratio.width / ratio.height) {
  //   zoom = (ratio.width / ratio.height) * (window.innerHeight / window.innerWidth) * zoom
  // }
  // document.querySelector('.reveal').style.width = `${zoom}vw`;
}

function updateMarkdownContent(markdown) {
  $('.slides').find('.section').remove();
  $('.slides').html(`<section data-markdown data-separator="^--- *$" data-separator-vertical="^-- *$" data-separator-notes="^Notes?:" data-charset="utf-8"><textarea data-template>${markdown}</textarea></section>`);
  if (!revealInited) {
    initializeReveal((mode == 'preview') || (mode == 'edit') || showNotes);
  }
}

function insertCaptionReference() {
  $('.slides').append(`<div id="caption" style="position: absolute; z-index: 100; bottom: 4%; width: 100%; font-size: 28px"></div>`);
}

function insertLogo() {
  $('.slides').append(`<div class="logo">xSlides</div>`);
}

function scrollToCurrentSlide() {
  var i = Reveal.getIndices();
  if (!i || i.h === undefined) {
    i = { h: 0, v: 0, f: 0 };
  }
  scrollToSlide(i);
}

function showCaption(text) {
  if (toggleCaption) {
    Reveal.getSlidesElement().querySelector("#caption").innerText = text;
  }
}

function scrollToSlide(i) {
  Reveal.slide(i.h, i.v, i.f);
}

function scrollToNextSlide() {
  if (Reveal.isLastSlide()) {
    return null;
  }

  showCaption('');
  Reveal.next();

  return Reveal.getCurrentSlide();
}

function markdownUpdate(markdown) {
  updateMarkdownContent(markdown);
  Reveal.getPlugin('markdown').init(Reveal);
  Reveal.getPlugin('katex').init(Reveal);

  setTimeout(() => Reveal.getPlugin('cardify').transformAllSlides(), 500);

  var event = new CustomEvent('revealupdated');
  document.dispatchEvent(event);

  // highlightAnyCodeBlocks();
  setTimeout(() => {
    scrollToCurrentSlide();
  }, 100);
}

function slideUpdate(content) {
  let currentSlide = Reveal.getCurrentSlide();

  if(!currentSlide) {
    sendMessageToParent({type: 'resetMarkdown'});
    return;
  }

  currentSlide.innerHTML = `<textarea data-template>${content}</textarea>`;
  currentSlide.removeAttribute('data-markdown-parsed');
  currentSlide.setAttribute('data-separator', '^--- *$');
  currentSlide.setAttribute('data-separator-vertical', '^-- *$');
  currentSlide.setAttribute('data-separator-notes', '^Notes?:');

  Reveal.getPlugin('markdown').init(Reveal);
  Reveal.getPlugin('katex').init(Reveal);

  setTimeout(() => Reveal.getPlugin('cardify').transformAllSlides(), 500);

  var event = new CustomEvent('slideupdated');
  document.dispatchEvent(event);
}

function externalLinksInNewWindow() {
  $(document.links).filter(function () {
    return this.hostname != window.location.hostname;
  }).attr('target', '_blank');
}

// if (mode !== 'edit') {
  if(hid) {
    insertMarkdownReference();
  }
// }
insertCaptionReference();
// insertLogo();
initializeReveal((mode == 'preview') || (mode == 'edit') || showNotes);


function clean(node) {
  //clean comments element from node
  for (var n = 0; n < node.childNodes.length; n++) {
    var child = node.childNodes[n];
    if (child.nodeType === 8) {
      node.removeChild(child);
      n--;
    } else if (child.nodeType === 1) {
      clean(child);
    }
  }
}

function getNotesText(slide) {
  var text = "";
  var div = document.getElementById('notes-holder');
  if (!div) {
    div = document.createElement("div");
    div.setAttribute("id", "notes-holder");
    div.style.display = "none";
  }

  if (slide.getAttribute('read-slide-text')) {
    div.innerHTML = slide.innerHTML;
    clean(div);
    text = getElementText(div);
  } else {
    var notes = Reveal.getSlideNotes(slide);
    if (notes) {
      div.innerHTML = notes;
      text = div.innerText || div.textContent || "";
    }
  }

  return text;
}

function replaceUnreadableContent(ele) {
  const replaces = [{ tag: 'code', text: ' code block' },
  { tag: 'style', text: '' }];

  replaces.forEach(replace => Array.from(ele.getElementsByTagName(replace.tag)).forEach(child => child.innerText = replace.text));
}

function getElementText(ele) {
  replaceUnreadableContent(ele);

  var text = ele.innerText;
  if (text && !/[\.!;！？。；\?]$/.test(text.trim())) {
    text = text + '\n'
  }
  return text;
}

function countWords(str) {
  var matches = str.match(/[\u00ff-\uffff]|\S+/g);
  return matches ? matches.length : 0;
}

// This is the same code with server side captions breaker, please keep it same with the server one
function breakNoteToCaptions(note) {
  var re = /[^\.!;！？。；\?\n]+[\.!;！？。；\?\n]+["'”]?|.+$/g;
  var sentences = note.match(re);

  if (!sentences) {
    return [note];
  }

  sentences = sentences.map(sentence => sentence.replace(/^\s+|\s+$/g, ''));
  sentences = sentences.filter(sentence => sentence.trim())

  // won't split further. because I can't control the break time of tts after split......
  // var short_sentences = [];
  // sentences.forEach(sentence => {
  //   if (countWords(sentence) < 16) {
  //     short_sentences.push(sentence);
  //     return;
  //   }
  //   sentence = sentence.replace(/[,，]/gi, "，》》》》");
  //   sents = sentence.split('》》》》');
  //   short_sentences = short_sentences.concat(sents);
  // });

  // sentences = short_sentences;

  if (sentences.length <= 1) {
    return sentences;
  }

  var captions = [];
  var toCheck = sentences[0];
  for (let index = 1; index < sentences.length; index++) {
    if (sentences[index].match(/^\d/) && (toCheck.slice(-1) === '.' || toCheck.slice(-1) === ',')
      || toCheck.match(/^[0-9]+\.$/)) {
      toCheck = toCheck + sentences[index];
    } else {
      captions.push(toCheck);
      toCheck = sentences[index];
    }
  }

  if (toCheck) {
    captions.push(toCheck);
  }

  return captions;
}

function readCaptions(captions, onEnd) {
  if (!captions || captions.length === 0) {
    onEnd();
    return;
  }

  let text = captions.shift();
  showCaption(text);
  // setTimeout(()=>onEnd(), 100)
  speakText(text, (event) => readCaptions(captions, onEnd));
}

function ttsFinished() {
  if (onTtsFinished) {
    onTtsFinished();
  }
}

let onTtsFinished = null;

function speakText(text, onEnd) {
  onTtsFinished = onEnd;
  return sendMessageToNative({ type: 'ttsSpeak', text });
}

var slidesFinished = false;

function autoScrollToNextSlide(time) {
  if (debugLog) {
    console.log('is last slide?.......', Reveal.isLastSlide(), Reveal.getProgress())
  }
  if (Reveal.getProgress() == 1) {
    slidesFinished = true;
    return stopSlidesPlay();
  }

  setTimeout(() => scrollToNextSlide(), time)
}

function stopSlidesPlay() {
  setSlidesPlaying(false);
  sendMessageToNative({ type: 'slidesFinished' });
}

// if(debugLog) {
// console.log(breakNoteToCaptions(`《定风波·莫听穿林打叶声》为苏轼于“乌台诗案”幸免于难后被贬黄州时所作。 test 1. 词前有小序，“三月七日沙湖道中遇雨。雨具先去，同行皆狼狈，余独不觉。已而遂晴，故作此。”这里所云“沙湖”在黄州东南30里。那一天苏轼在去沙湖路上遇雨，本来是带着雨具的，但途中以为不需要就让人带走了。不料后来竟下起雨来，同行的人一下子就被雨给打乱了：我的衣服要湿了，我的鞋子要脏了！心里先紧张起来。但苏轼觉得，不管紧张还是不紧张，雨始终都要打到身上来，又何必为这件事情而狼狈呢？所以他说：“同行皆狼狈，余独不觉。”这就是苏东坡之所以为苏东坡了——他有一种达观的、超然的思想：狂风骤雨不会久长，紧张和狼狈也于事无补。“已而遂晴”，果然没多久，就雨过天晴了。苏轼联想到自己的遭遇，“故作此”。33.25 Yes. This is good? Good.`))
// }
var slidesPlaying = false;

function play() {
  if (slidesFinished) {
    var slide = { h: 0, v: 0 };
    scrollToSlide(slide);
  }

  playSlide();

  slidesFinished = false;
  setSlidesPlaying(true);
}

function setVideoAutoPlay(autoplay) {
  Array.from(document.getElementsByTagName("video")).map(ele => {
    if (autoplay) {
      ele.setAttribute('data-autoplay', '');
    } else {
      ele.removeAttribute('data-autoplay');
    }
  });
}

var playPaused = false;

function pause() {
  playPaused = true;

  if (!playList || playList.length === 0) {
    return;
  }

  playList[0].pause();
  sendMessageToNative({ type: 'pause' })
}

function resume() {
  if (Reveal.isPaused()) {
    Reveal.togglePause();
  }

  playPaused = false;

  if (!playList || playList.length === 0) {
    autoScrollToNextSlide(300);
    return;
  }

  playList[0].resume();
  sendMessageToNative({ type: 'resume' })
}

function setSlidesPlaying(playing) {
  slidesPlaying = playing;
  sendMessageToNative({ type: 'slidesPlaying', value: playing });
}

var playList = [];

Reveal.on('paused', () => {
  if (slidesPlaying && !playPaused) {
    pause();
  }
});

Reveal.on('resumed', () => {
  if (playPaused) {
    resume();
  }
});

Reveal.on('slidetransitionend', event => {
  slidesFinished = false;

  if (playList && playList.length > 0) {
    playList[0].cancel();
  }

  setTimeout(() => {
    if (!slidesPlaying) {
      togglePlayButton(false);
      showPlayButtonWhenFirstAudioReadyToPlay();

      return;
    }

    if (playPaused) {
      setSlidesPlaying(false)
      playPaused = false;
    }

    showCaption('');

    if (!slidesPlaying) {
      return;
    }

    playSlide();
  }, 300);
});

Reveal.on('ready', event => {
  showPlayButtonWhenFirstAudioReadyToPlay();
});


const preloadFollowingSlidesMedia = () => {
  let slide;
  const slides = Reveal.getSlides();
  for (let index = 0; index < slides.length; index++) {
    const element = slides[index];
    if (element.classList.contains('present') && index + 1 < slides.length) {
      slide = slides[index + 1];
      break;
    }
  }

  if (!slide) {
    return;
  }

  Array.from(slide.getElementsByTagName('audio')).forEach(ele => {
    if (ele.getAttribute('loadinited') || ele.readyState > 2 || ele.networkState === 2) {
      return;
    }

    ele.load();
    ele.setAttribute('loadinited', true);

    if (debugLog) {
      console.log('loading.......', ele.innerText)
    }
  });
}

const showPlayButtonWhenFirstAudioReadyToPlay = () => {
  const slide = Reveal.getCurrentSlide();
  if(!slide) {
    return;
  }

  let audios = Array.from(slide.getElementsByTagName('audio'));

  if (!audios || audios.length === 0) {
    return togglePlayButton(true);
  }

  var ele = audios[0];
  if (debugLog) {
    console.log('showPlayButtonWhenFirstAudioReadyToPlay', ele.readyState, ele.networkState, ele.innerText);
  }

  if (ele.readyState < 2 && retried < 8) {
    if (retried === 0) {
      ele.load();
    }

    retried++;
    setTimeout(() => showPlayButtonWhenFirstAudioReadyToPlay(), 1000);
    return false;
  }

  retried = 0;

  return togglePlayButton(true);
}

togglePlayButton = (isShow) => {
  sendMessageToNative({ type: 'togglePlayButton', value: isShow })
}

const isTtsSupported = () => {
  return 'speechSynthesis' in window;
}

const isPlaySupported = () => {
  const slide = Reveal.getCurrentSlide();
  const isTtsBuilt = slide.getAttribute('tts-built');
  return isTtsSupported() || isTtsBuilt;
}

var playedMediaList = [];
const mediaPlay = (ele) => {
  if (playPaused) {
    return;
  }

  if (ele.id) {
    if (debugLog) {
      console.log('ele.id when media play', ele.id, playedMediaList.indexOf(ele.id), ele.innerText);
    }

    if (playedMediaList.indexOf(ele.id) > -1) {
      if (debugLog) {
        console.log('ele already played', ele.id);
      }
      return;
    }

    playedMediaList.push(ele.id);
  }

  if (debugLog) {
    console.log('mediaPlay', ele.readyState, ele.networkState, ele.innerText);
  }
  var playPromise = ele.play();

  // In browsers that don’t yet support this functionality,
  // playPromise won’t be defined.
  if (playPromise !== undefined) {
    playPromise.then(function () {
      // Automatic playback started!
      preloadNextMedia();
    }).catch(function (error) {
      // Automatic playback failed.
      // Show a UI element to let the user manually start playback.
      stopSlidesPlay();

      if (ele.id) {
        let index = playedMediaList.indexOf(ele.id);
        playedMediaList.splice(index, 1);
      }
      if (debugLog) {
        console.log('err in mediaPlay', error)
      }
    });
  }
}

const mediaRetry = (ele) => {
  if (debugLog) {
    console.log('mediaRetry', ele.readyState, ele.networkState, ele.innerText);
  }
  // if (ele.networkState === 2) {
  //   return setTimeout(() => mediaPlay(ele), 1500);
  // }

  ele.addEventListener('canplaythrough', (event) => {
    if (debugLog) {
      console.log('canplaythrough', ele.readyState, ele.networkState, ele.innerText);
    }
    if (playPaused) {
      return;
    }
    mediaPlay(ele);
  });

  var loadPromise = ele.load();
  if (loadPromise) {
    loadPromise.then(() => { }).catch((error) => {
      console.log('err in mediaRetry load', error)
    })
  }
}

var retried = 0;

const playIt = (ele, onEnd) => {
  if (debugLog) {
    console.log('playIt', ele.readyState, ele.networkState, ele.innerText);
  }

  if (ele.networkState === 2 && ele.readyState < 2 && retried < 3) {
    retried++;
    return setTimeout(() => playIt(ele, onEnd), 1500);
  }

  retried = 0;

  if (onEnd) {
    ele.onended = () => {
      onEnd();
      setTimeout(() => restoreMediaElement(ele), 100);
    }
  }

  if (ele.readyState === 0 || ele.networkState === 0) {
    mediaRetry(ele)
  } else {
    mediaPlay(ele);
  }

  showCaption(ele.innerText || '');
}

const preloadNextMedia = () => {
  if (!playList || playList.length < 2) {
    return;
  }

  playList[1].preload && playList[1].preload();
}

const restoreMediaElement = (ele) => {
  ele.onended = null;
  ele.pause();
  ele.currentTime = 0;
}

function ttsInsteadOfAudio(ele) {
  playList.shift();

  let idleTime = 300;
  if (ele.innerText) {
    if (isTtsSupported() || tts_native) {
      playList.unshift({
        play: (onEnd) => {
          speakText(ele.innerText, onEnd);
        },
        pause: () => sendMessageToNative({ type: 'ttsPause' }),
        resume: () => sendMessageToNative({ type: 'ttsResume' }),
        cancel: () => sendMessageToNative({ type: 'ttsCancel' })
      });
      idleTime = 0;
    } else {
      idleTime = 2200;
    }
  }

  restoreMediaElement(ele);
  setTimeout(() => doPlayList(), idleTime);
}

function playSlide() {
  if (debugLog) {
    console.log('play slide');
  }
  playList = [];
  playedMediaList = [];

  preloadFollowingSlidesMedia();

  const slide = Reveal.getCurrentSlide();
  slide.querySelectorAll("video, audio").forEach((ele, index) => {
    ele.onerror = (error) => {
      console.log('audio on error', ele.readyState, ele.networkState, ele.innerText, ele.error.message);
      // if (!ele.getAttribute('onerror_retried')) {
      //   mediaRetry(ele);
      //   ele.setAttribute('onerror_retried', true);
      // } else {
      ttsInsteadOfAudio(ele);

      // }
    };

    let indices = Reveal.getIndices();

    ele.id = `h${indices.h}v${indices.v}f${indices.f}i${index}`;
    playList.push({
      play: (onEnd) => {
        // ele.setAttribute('data-autoplay', '');
        // Reveal.slide();
        playIt(ele, onEnd);
      },
      pause: () => {
        ele.pause();
      },
      resume: () => {
        ele.play();
      },
      cancel: () => {
        restoreMediaElement(ele);
      },
      preload: () => {
        if (ele.readyState === 0 && ele.networkState !== 2) {
          ele.load();
        }
      }
    })
  });

  if (!slide.getAttribute("disable-client-tts")) {
    let note = getNotesText(slide);
    if (Reveal.isFirstSlide() && !note) {
      note = "\n";
    }

    note && playList.push({
      play: (onEnd) => {
        let captions = breakNoteToCaptions(note);
        readCaptions(captions, onEnd);
      },
      pause: () => sendMessageToNative({ type: 'ttsPause' }),
      resume: () => sendMessageToNative({ type: 'ttsResume' }),
      cancel: () => sendMessageToNative({ type: 'ttsCancel' })
    });
  }

  if (!playList || playList.length === 0) {
    return autoScrollToNextSlide(3000);
  }

  doPlayList();
}

function doPlayList() {
  if (!playList || playList.length === 0) {
    autoScrollToNextSlide(200);
    return;
  }

  let item = playList[0];
  item.play(() => {
    if (debugLog) {
      console.log('onEnd')
    }
    playList.shift();
    doPlayList();
  });
}

function theme_changed(theme_name) {
  var themeStyleEle = document.getElementById("theme");
  const index = themes.findIndex(theme => theme.name == theme_name);

  themeStyleEle.href = themes[index > -1 ? index : 0].stylesheet;
}

const theme = getUrlParameter("theme");
theme_changed(theme || 'black')

function loadSlidesConfig() {
  $.ajax(server_host + "/slides/getSlidesConfig?hid=" + hid, {
    type: 'get',
    contentType: 'application/json; charset=utf-8',
    dataType: "json",
  }).done((data) => {
    if (data.success && data.data) {
      theme_changed(data.data.theme);
    }

  }).fail((jqXHR, textStatus, errorThrown) => {
    console.log('error', textStatus, errorThrown);
  });
}

$(function () {
  if (mode != 'article' && !theme) {
    loadSlidesConfig();
  }
});

function sendMessageToNative(message) {
  try {
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(JSON.stringify(message));
    } else {
      window.parent.postMessage(JSON.stringify(message), '*');
    }
  } catch (err) {
    if (debugLog) {
      console.log(err);
    }
  }
}

// Monkey patch Reveal so we can reload markdown through an
// inter window message (using the reveal rpc api)
// (yes, reveal has an rpc api!)
// see save.js
// Reveal.markdownUpdate = markdownUpdate;
