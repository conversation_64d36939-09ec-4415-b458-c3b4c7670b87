<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>RevealCardify with SmartLayout Integration Test</title>
    <link rel="stylesheet" href="dist/reset.css">
    <link rel="stylesheet" href="dist/reveal.css">
    <link rel="stylesheet" href="dist/theme/black.css">
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- 测试1: 纯文本列表（应该使用 Cardify 功能） -->
            <section>
                <h2>测试1: 纯文本列表</h2>
                <ul>
                    <li>第一个列表项
                        <ul>
                            <li>嵌套项目1</li>
                            <li>嵌套项目2</li>
                        </ul>
                    </li>
                    <li>第二个列表项
                        <ul>
                            <li>嵌套项目3</li>
                            <li>嵌套项目4</li>
                        </ul>
                    </li>
                    <li>第三个列表项</li>
                </ul>
            </section>

            <!-- 测试2: 图文混排（应该使用 SmartLayout 功能） -->
            <section>
                <h2>测试2: 图文混排</h2>
                <p>这是一段文本内容，应该被放在卡片中。</p>
                <ul>
                    <li>列表项目1</li>
                    <li>列表项目2</li>
                    <li>列表项目3</li>
                </ul>
                <img src="https://via.placeholder.com/300x200/0066cc/ffffff?text=Test+Image" alt="测试图片">
            </section>

            <!-- 测试3: 多图片和文本 -->
            <section>
                <h2>测试3: 多图片和文本</h2>
                <p>这是另一段文本内容。</p>
                <ol>
                    <li>有序列表项目1</li>
                    <li>有序列表项目2</li>
                </ol>
                <img src="https://via.placeholder.com/200x150/cc6600/ffffff?text=Image+1" alt="图片1">
                <img src="https://via.placeholder.com/200x150/006600/ffffff?text=Image+2" alt="图片2">
            </section>

            <!-- 测试4: 简单列表（应该使用列表风格卡片） -->
            <section>
                <h2>测试4: 简单列表</h2>
                <ul>
                    <li>简单列表项目1</li>
                    <li>简单列表项目2</li>
                    <li>简单列表项目3</li>
                    <li>简单列表项目4</li>
                </ul>
            </section>

            <!-- 测试5: 只有图片（不应该处理） -->
            <section>
                <h2>测试5: 只有图片</h2>
                <img src="https://via.placeholder.com/400x300/660066/ffffff?text=Only+Image" alt="只有图片">
            </section>

            <!-- 测试6: 只有文本（不应该处理） -->
            <section>
                <h2>测试6: 只有文本</h2>
                <p>这是一段纯文本内容，没有列表，也没有图片。</p>
                <p>应该保持原样显示。</p>
            </section>
        </div>
    </div>

    <script src="dist/reveal.js"></script>
    <script src="plugin/cardify/plugin.js"></script>
    <script>
        Reveal.initialize({
            hash: true,
            plugins: [RevealCardify]
        });
    </script>
</body>
</html>
